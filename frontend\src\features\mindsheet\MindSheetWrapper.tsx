/**
 * MindSheetWrapper Component
 *
 * A wrapper component that handles all store access for MindSheet.
 * This component ensures that Zustand store hooks are called at the top level
 * and passes down store state and actions as props to the MindSheet component.
 *
 * This follows the container/presentational component pattern where:
 * - MindSheetWrapper (container) handles store access and data fetching
 * - MindSheet (presentational) focuses on rendering based on props
 */

import React, { useEffect, useState } from 'react';
import MindSheet, { MindSheetProps } from './MindSheet';
import { MindSheetContentType } from '../../core/state/StoreTypes';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import {
  getMindMapStore,
  getMindBookStore,
  getChatForkStore,
  getSheetMindMapStore,
  hasSheetMindMapStore
} from '../../core/services/StoreService';

interface MindSheetWrapperProps {
  id: string;
  title: string;
  contentType: MindSheetContentType;
  isActive: boolean;
  content: any;
  onActivate?: () => void;
}

const MindSheetWrapper: React.FC<MindSheetWrapperProps> = (props) => {
  // State to hold store references - initialize with null to avoid hook violations
  const [mindMapStore, setMindMapStore] = useState<any>(null);
  const [mindBookStore, setMindBookStore] = useState<any>(null);
  const [chatForkStore, setChatForkStore] = useState<any>(null);
  const [sheetStore, setSheetStore] = useState<any>(null);

  // Initialize stores when the wrapper mounts
  useEffect(() => {
    console.log('MindSheetWrapper: Mounted for sheet:', props.id, 'contentType:', props.contentType);

    try {
      // Get all stores using the StoreService
      const mindMapStoreRef = getMindMapStore();
      const mindBookStoreRef = getMindBookStore();
      const chatForkStoreRef = getChatForkStore();

      setMindMapStore(mindMapStoreRef);
      setMindBookStore(mindBookStoreRef);
      setChatForkStore(chatForkStoreRef);

      // Get the sheet-specific store
      const sheetStoreRef = getSheetMindMapStore(props.id);
      setSheetStore(sheetStoreRef);

      // Ensure the store exists for this sheet
      if (props.contentType === MindSheetContentType.MINDMAP) {
        if (!hasSheetMindMapStore(props.id)) {
          console.log('MindSheetWrapper: Creating new store for sheet:', props.id);
        } else {
          console.log('MindSheetWrapper: Using existing store for sheet:', props.id);
        }

        // Register the sheet creation event if it's active
        if (props.isActive) {
          RegistrationManager.registerEvent(EventType.SHEET_ACTIVATED, {
            id: props.id,
            type: props.contentType.toLowerCase()
          });
        }
      }
    } catch (error) {
      console.error('MindSheetWrapper: Error initializing stores for sheet:', props.id, error);
    }

    return () => {
      console.log('MindSheetWrapper: Unmounted for sheet:', props.id);
    };
  }, [props.id, props.contentType, props.isActive]);

  // Pass down all props and store references to the MindSheet component
  // Only render if we have all required stores
  if (!mindMapStore || !mindBookStore || !chatForkStore) {
    return <div className="loading-content">Initializing stores...</div>;
  }

  return (
    <MindSheet
      {...props}
      mindMapStore={mindMapStore}
      mindBookStore={mindBookStore}
      chatForkStore={chatForkStore}
      sheetStore={sheetStore}
    />
  );
};

export default MindSheetWrapper;
